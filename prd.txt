**Product Requirements Document (PRD)**

**Title:** AI-Powered SQL Assistant with Schema-Aware Retrieval-Augmented Generation (RAG)

**Overview:**
A backend-only AI SQL Assistant service that allows users to chat naturally and retrieve precise, schema-aware answers based on their unique SQL database structure. This system supports multi-tenant architecture, stores chat memory per user, uses schema-based embeddings for better RAG, and allows OpenAI-powered contextual querying.

---

**Goals:**

* Enable users to ask natural language questions about their database.
* Store user-specific chat history for context.
* Understand and embed the user's SQL schema (table names, fields, relationships).
* Support scalable, multi-tenant architecture with isolated vector storage per user.
* Use OpenAI embeddings and completions for contextual understanding and answering.

---

**Tech Stack:**

* **Backend Framework:** FastAPI
* **LLM & Embeddings:** OpenAI (e.g. `gpt-4`, `text-embedding-3-small`)
* **Vector DB:** Pinecone (per-user namespace)
* **Chat Memory Store:** Redis (user + session scoped)
* **Relational DB:** PostgreSQL (SQLAlchemy for schema extraction)
* **ORM:** SQLAlchemy
* **Environment Config:** dotenv

---

**Core Features:**

### 1. User Schema Upload & Embedding

* Users provide database URL or credentials.
* App uses SQLAlchemy to introspect schema: tables, columns, types, and relationships.
* Generates natural language descriptions for each table/field.
* Embeds these schema descriptions using OpenAI embeddings.
* Stores embeddings in Pinecone under the user-specific namespace (multi-tenant).

### 2. Chat Interface (API-Only)

* User sends a prompt (e.g., "Which customers haven’t placed orders this month?").
* System retrieves schema context via similarity search in Pinecone.
* Fetches recent chat history (from Redis) for conversational context.
* Assembles system + user + memory + schema context messages.
* Sends messages to OpenAI for completion.
* Stores full conversation in Redis (persisted optionally in DB).

### 3. Multi-Tenant Design

* Every user has:

  * A unique Redis namespace for chat memory.
  * A unique Pinecone namespace for schema embeddings.
  * Optionally, a DB table storing metadata (e.g., user\_id, email).

### 4. APIs

#### `/embed_schema` \[POST]

* Input: `db_uri`, `user_id`
* Connects to the DB using SQLAlchemy.
* Extracts and embeds schema.
* Stores embeddings in Pinecone.

#### `/chat` \[POST]

* Input: `user_id`, `message`
* Retrieves latest chat history from Redis.
* Retrieves relevant schema info from Pinecone.
* Sends structured message list to OpenAI.
* Returns completion + appends it to Redis memory.

#### `/reset_chat` \[POST]

* Clears the Redis memory for a specific user.

---

**System Prompt Format:**
("schema-aware assistant" style)

```text
You are an intelligent SQL assistant. Be concise, helpful, and accurate. Use the user-provided schema context to answer questions. If you don’t know something, say you don't know. Never hallucinate.
```

---

**Example Flow:**

1. User uploads DB credentials via `/embed_schema`.
2. App extracts: `orders(id, user_id, total_amount)`, `users(id, name)`
3. App generates descriptions:

   * "The `orders` table contains all customer orders with total amount."
4. Descriptions are embedded and stored in Pinecone.
5. User chats via `/chat`:

   * Q: "Which users spent the most last month?"
   * Schema is retrieved from Pinecone.
   * Chat history is pulled from Redis.
   * Final message set is sent to OpenAI.
6. Completion returned and stored in chat memory.

---

**Non-Functional Requirements:**

* All schema embeddings should use OpenAI batch mode for speed.
* Redis chat history should expire after N days unless marked persistent.
* Rate-limit API per user via token bucket or similar.
* Secure all endpoints via token or session-based auth (optional).

---

**Future Additions:**

* WebSocket endpoint for real-time chat.
* Export chat transcript.
* Support for multiple schemas per user.
* Add tracing with LangSmith or OpenTelemetry.
* Optionally allow schema upload via `.sql` dump or plain text.

---

**Conclusion:**
This backend-only AI SQL assistant is optimized for reliability, schema accuracy, and chat context. It avoids over-dependence on libraries like LangChain, providing production-level stability and full control over embedding and retrieval pipelines.
