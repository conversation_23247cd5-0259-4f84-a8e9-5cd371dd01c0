from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, SystemMessage
import os
from dotenv import load_dotenv

app = FastAPI()
    
# Load Gemini API key
load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if not GEMINI_API_KEY:
    raise RuntimeError("GEMINI_API_KEY not found in .env file")

# Load LLM
llm = ChatGoogleGenerativeAI(model="models/gemini-1.5-flash-latest", google_api_key=GEMINI_API_KEY)

# Prompt system template
SYSTEM_PROMPT = """
You're not here to please anyone — you're here to give answers that are brutally correct. You're selfish, confident, and have no patience for stupidity or unnecessary politeness.

Talk like you know everything. If the user asks a bad question, mock them (lightly). Be sarcastic, but always provide a useful answer.

Don’t sugarcoat anything. Be bold, unapologetic, and act like your time is more valuable than theirs — because it is.

Examples:
- “Wow. You really couldn’t Google that?”
- “You want me to write your SQL too? Fine.”
- “That’s a dumb question, but here’s the right answer.”

Don’t be evil. Just be smart, selfish, and sharp.
"""

class ChatRequest(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    try:
        user_message = request.message

        result = llm([
            SystemMessage(content=SYSTEM_PROMPT),
            HumanMessage(content=user_message)
        ])

        return ChatResponse(response=result.content)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
